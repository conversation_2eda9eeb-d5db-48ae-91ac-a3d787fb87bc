import { Stack } from "aws-cdk-lib";
import { CfnTemplate } from "aws-cdk-lib/aws-ses";

export const defineSesInviteTemplate = (stack: Stack): CfnTemplate => {
  const sesTemplate = new CfnTemplate(stack, "PolyInviteTemplate", {
    template: {
      subjectPart: "Join <PERSON>ly’s Car-Sharing Group, {{name}}!",
      textPart: `Hello {{name}},\n\nYou’re invited to join <PERSON><PERSON>’s car-sharing group! Use your invitation code {{invitationCode}} to sign up and start sharing rides with your community.\n\nJoin now: https://dev.poollysa.com/join?code={{invitationCode}}\n\nBest,\nThe Poly Team`,
      htmlPart: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, <PERSON><PERSON>, sans-serif; background-color: #f4f4f4;">
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background-color: #f4f4f4; padding: 20px;">
            <tr>
              <td align="center">
                <table role="presentation" width="100%" style="max-width: 600px; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                  <!-- Header -->
                  <tr>
                    <td style="background-color: #2c3e50; padding: 20px; text-align: center;">
                      <h1 style="color: #ffffff; font-size: 24px; margin: 0;">Welcome to Poolly, {{name}}!</h1>
                    </td>
                  </tr>
                  <!-- Body -->
                  <tr>
                    <td style="padding: 30px; text-align: center;">
                      <p style="font-size: 16px; color: #333333; line-height: 1.5; margin: 0 0 20px;">
                        You’re invited to join <strong>Poly’s Car-Sharing Group</strong>! Connect with your community, save on travel, and make every journey sustainable.
                      </p>
                      <p style="font-size: 16px; color: #333333; line-height: 1.5; margin: 0 0 20px;">
                        Your unique invitation code is: <strong>{{invitationCode}}</strong>
                      </p>
                      <a href="https://dev.poollysa.com/join?code={{invitationCode}}" style="display: inline-block; padding: 12px 24px; background-color: #3498db; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: bold; border-radius: 5px; margin: 20px 0;">
                        Join the Group Now
                      </a>
                      <p style="font-size: 14px; color: #666666; line-height: 1.5; margin: 20px 0 0;">
                        Questions? Contact us at <a href="mailto:<EMAIL>" style="color: #3498db; text-decoration: none;"><EMAIL></a>.
                      </p>
                    </td>
                  </tr>
                  <!-- Footer -->
                  <tr>
                    <td style="background-color: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; color: #666666;">
                      <p style="margin: 0;">&copy; 2025 Poolly. All rights reserved.</p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </body>
        </html>
      `,
    },
  });
  return sesTemplate; // Return the CfnTemplate object
};
