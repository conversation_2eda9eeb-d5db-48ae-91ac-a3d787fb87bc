"use server";

import { db } from "../db";
import { eq, and, inArray } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  company,
  companyOwnership,
  party,
  vehicleVariant
} from "../drizzle/schema";
import type {
  VehicleToGroupRequest,
} from "@/types/vehicle-groups";
import { getUserAttributes } from "@/lib/serverUserAttributes";

export async function addVehicleToGroup(
  request: VehicleToGroupRequest,

): Promise<{ success: boolean; message: string; data?: any }> {
  try {
    const userAttributes = await getUserAttributes();
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    if (!dbId) {
      return {
        success: false,
        message: "User not authenticated"
      };
    }

    // Validate disclaimers are accepted
    if (!request.ownership_disclaimer_accepted || !request.ownership_confirmation) {
      return {
        success: false,
        message: "You must accept the ownership disclaimer and confirm your ownership"
      };
    }

    // Verify the company exists
    const companyResults = await db
      .select({ id: company.id, partyId: company.partyId })
      .from(company)
      .where(eq(company.id, request.company_id))
      .limit(1)
    

    if (companyResults.length === 0) {
      return {
        success: false,
        message: "Group not found"
      };
    }

    // Check if user is already a member of this company
    const existingOwnership = await db
      .select({ id: companyOwnership.id })
      .from(companyOwnership)
      .where(
        and(
          eq(companyOwnership.partyId, +dbId),
          eq(companyOwnership.companyId, request.company_id),
          eq(companyOwnership.isActive, true)
        )
      )
      .limit(1);

    // Create or insert the vehicle record
    const vehicleData = {
      partyId: companyResults[0].partyId,
      modelId: request.model_id,
      vinNumber: request.vin_number,
      vehicleRegistration: request.vehicle_registration,
      countryOfRegistration: request.country_of_registration,
      manufacturingYear: request.manufacturing_year,
      purchaseDate: request.purchase_date,
      color: request.color,
      isActive: true,
    };

    const insertedVehicle = await db.insert(vehicles).values(vehicleData).returning();

    // If not already a member, create ownership record
    if (existingOwnership.length === 0) {
      await db.insert(companyOwnership).values({
        partyId: +dbId,
        companyId: request.company_id,
        fraction: "1.0", // Initially, the owner has 100% of this vehicle
        effectiveFrom: new Date().toISOString(),
        isActive: true,
      });
    }

    return {
      success: true,
      message: "Vehicle successfully added to group",
      data: {
        vehicle_id: insertedVehicle[0]?.id,
        group_id: request.company_id
      }
    };

  } catch (error) {
    console.error("Error adding vehicle to group:", error);
    return {
      success: false,
      message: "Failed to add vehicle to group. Please try again."
    };
  }
}

export async function getVehiclesInGroup(companyId: number) {
  try {
    // Get all party IDs that are members of this company
    const memberPartyIds = await db
      .select({ partyId: companyOwnership.partyId })
      .from(companyOwnership)
      .where(
        and(
          eq(companyOwnership.companyId, companyId),
          eq(companyOwnership.isActive, true)
        )
      );

    if (memberPartyIds.length === 0) {
      return [];
    }

    const partyIds = memberPartyIds.map(ownership => ownership.partyId);

    // Get vehicles owned by group members with make/model details
    const groupVehicles = await db
      .select({
        id: vehicles.id,
        vinNumber: vehicles.vinNumber,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryId,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        modelName: vehicleModel.model,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(inArray(vehicles.partyId, partyIds))
      .orderBy(vehicles.id);

    return groupVehicles.map(vehicle => ({
      id: vehicle.id,
      party_id: vehicle.partyId,
      model_id: vehicle.modelId,
      vin_number: vehicle.vinNumber,
      vehicle_registration: vehicle.vehicleRegistration,
      country_of_registration: vehicle.countryOfRegistration,
      manufacturing_year: vehicle.manufacturingYear,
      purchase_date: vehicle.purchaseDate,
      color: vehicle.color,
      is_active: vehicle.isActive,
      model: {
        id: vehicle.modelId,
        make_id: vehicle.makeId,
        model: vehicle.modelName,
        year_model: null,
        transmission: null,
        make: {
          id: vehicle.makeId,
          name: vehicle.makeName
        }
      },
      bookings: [], // TODO: Add bookings if needed
      maintenance_items: [], // TODO: Add maintenance if needed
      inspections: [], // TODO: Add inspections if needed
      media: [], // TODO: Add media if needed
      vehicle_documents: [] // TODO: Add documents if needed
    }));

  } catch (error) {
    console.error("Error getting vehicles in group:", error);
    return [];
  }
}

export async function getVehicleMakes() {
  try {
    const makes = await db
      .select({
        id: vehicleMake.id,
        name: vehicleMake.name,
        description: vehicleMake.description,
        isActive: vehicleMake.isActive,
        createdAt: vehicleMake.createdAt,
        updatedAt: vehicleMake.updatedAt,
      })
      .from(vehicleMake)
      .where(eq(vehicleMake.isActive, true))
      .orderBy(vehicleMake.name);

    return makes.map(make => ({
      id: make.id,
      name: make.name,
      country_of_origin: "Unknown", // Default value since this field might not exist in schema
      created_at: new Date(make.createdAt || new Date()),
      updated_at: new Date(make.updatedAt || new Date()),
    }));
  } catch (error) {
    console.error("Error getting vehicle makes:", error);
    return [];
  }
}

export async function getVehicleModelsByMake(makeId: number) {
  try {
    const models = await db
      .select({
        id: vehicleModel.id,
        makeId: vehicleModel.makeId,
        model: vehicleModel.model,
        firstYear: vehicleModel.firstYear,
        lastYear: vehicleModel.lastYear,
        description: vehicleModel.description,
        isActive: vehicleModel.isActive,
        createdAt: vehicleModel.createdAt,
        updatedAt: vehicleModel.updatedAt,
        makeName: vehicleMake.name,
      })
      .from(vehicleModel)
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(vehicleModel.makeId, makeId),
          eq(vehicleModel.isActive, true)
        )
      )
      .orderBy(vehicleModel.model);

    return models.map(model => ({
      id: model.id,
      make_id: model.makeId,
      model: model.model,
      year_model: model.firstYear, // Use firstYear as the base year for compatibility
      first_year: model.firstYear,
      last_year: model.lastYear,
      description: model.description,
      transmission: null, // This field may not exist in current schema
      is_active: model.isActive,
      created_at: new Date(model.createdAt || new Date()),
      updated_at: new Date(model.updatedAt || new Date()),
      make: {
        id: model.makeId,
        model: model.makeName,
        country_of_origin: "Unknown", // Default value
      }
    }));
  } catch (error) {
    console.error("Error getting vehicle models:", error);
    return [];
  }
}

export async function getVehicleVariantsByModel(modelId: number) {
  try {
    const variants = await db
      .select({
        id: vehicleVariant.id,
        modelId: vehicleVariant.modelId,
        name: vehicleVariant.name,
        trimName: vehicleVariant.trimName,
        year: vehicleVariant.year,
        engine: vehicleVariant.engine,
        drivetrain: vehicleVariant.drivetrain,
        bodyType: vehicleVariant.bodyType,
        seats: vehicleVariant.seats,
        doors: vehicleVariant.doors,
        msrp: vehicleVariant.msrp,
        features: vehicleVariant.features,
        specs: vehicleVariant.specs,
        fuelType: vehicleVariant.fuelType,
        transmission: vehicleVariant.transmission,
        description: vehicleVariant.description,
        isActive: vehicleVariant.isActive,
        createdAt: vehicleVariant.createdAt,
        updatedAt: vehicleVariant.updatedAt,
      })
      .from(vehicleVariant)
      .where(
        and(
          eq(vehicleVariant.modelId, modelId),
          eq(vehicleVariant.isActive, true)
        )
      )
      .orderBy(vehicleVariant.year);

    return variants.map(variant => ({
      id: variant.id,
      model_id: variant.modelId,
      name: variant.name,
      trim_name: variant.trimName,
      year: variant.year,
      engine: variant.engine,
      drivetrain: variant.drivetrain,
      body_type: variant.bodyType,
      seats: variant.seats,
      doors: variant.doors,
      msrp: variant.msrp,
      features: variant.features,
      specs: variant.specs,
      fuel_type: variant.fuelType,
      transmission: variant.transmission,
      description: variant.description,
      is_active: variant.isActive,
      created_at: variant.createdAt || new Date().toISOString(),
      updated_at: variant.updatedAt || new Date().toISOString(),
    }));
  } catch (error) {
    console.error("Error getting vehicle variants:", error);
    return [];
  }
}

export async function getCompanies() {
  try {
    const companies = await db
      .select({
        id: company.id,
        partyId: company.partyId,
        registrationNumber: company.registrationNumber,
        registrationCountry: company.countryId,
        registrationDate: company.registrationDate,
        name: company.name,
        description: company.description,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
      })
      .from(company)
      .orderBy(company.name);

    return companies.map(comp => ({
      id: comp.id,
      party_id: comp.partyId,
      registration_number: comp.registrationNumber,
      registration_country: comp.registrationCountry,
      registration_date: comp.registrationDate,
      name: comp.name || "Unnamed Group",
      created_at: comp.createdAt || new Date().toISOString(),
      updated_at: comp.updatedAt || new Date().toISOString(),
    }));
  } catch (error) {
    console.error("Error getting companies:", error);
    return [];
  }
} 