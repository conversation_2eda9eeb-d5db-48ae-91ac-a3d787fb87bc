"use server";

/**
 * DRIZZLE ACTIONS - ADMIN VEHICLE CATALOG
 *
 * This file contains all direct database operations for vehicle catalog management using Drizzle ORM.
 * Handles CRUD operations for h_vehicle_catalog and related tables (features, images, platforms).
 */

import { db } from "../../db";
import { eq, and, desc, asc, like, or, sql, inArray } from "drizzle-orm";
import {
  h_vehicleCatalog,
  h_vehicleCatalogFeatures,
  h_vehicleCatalogImages,
  h_vehicleCatalogPlatforms,
} from "@/drizzle/h_schema/vehicle-catalog";
import {
  h_listings,
  h_listing_approval_status,
  h_listing_publish_status,
} from "@/drizzle/h_schema/listings";
import { party } from "@/drizzle/schema";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// ==================== TYPES ====================

export interface VehicleCatalogItem {
  id: number;
  make: string;
  model: string;
  year: number;
  variant?: string;
  category: string;
  fuelType: string;
  ehailingEligible: boolean;
  estimatedPrice?: number;
  weeklyFeeTarget?: number;
  description?: string;
  specifications?: string;
  createdAt?: string;
  updatedAt?: string;
  features: string[];
  platforms: string[];
  images: Array<{
    id: number;
    imageUrl: string;
    isPrimary: boolean;
  }>;
}

export interface VehicleCatalogCreateData {
  make: string;
  model: string;
  year: number;
  variant?: string;
  category: string;
  fuelType: string;
  ehailingEligible?: boolean;
  estimatedPrice?: number;
  weeklyFeeTarget?: number;
  description?: string;
  specifications?: string;
  features: string[];
  platforms: string[];
  images: Array<{
    imageUrl: string;
    isPrimary: boolean;
  }>;
}

export interface VehicleCatalogSearchFilters {
  searchQuery?: string;
  category?: string;
  fuelType?: string;
  ehailingEligible?: boolean;
  make?: string;
  yearFrom?: number;
  yearTo?: number;
}

// ==================== FETCH OPERATIONS ====================

/**
 * Get all vehicle catalog items with their related data
 */
export async function getVehicleCatalogItems(
  filters?: VehicleCatalogSearchFilters
): Promise<VehicleCatalogItem[]> {
  try {
    // Build where conditions
    const whereConditions = [];

    if (filters?.searchQuery) {
      whereConditions.push(
        or(
          like(h_vehicleCatalog.make, `%${filters.searchQuery}%`),
          like(h_vehicleCatalog.model, `%${filters.searchQuery}%`),
          like(h_vehicleCatalog.variant, `%${filters.searchQuery}%`)
        )
      );
    }

    if (filters?.category) {
      whereConditions.push(eq(h_vehicleCatalog.category, filters.category));
    }

    if (filters?.fuelType) {
      whereConditions.push(eq(h_vehicleCatalog.fuelType, filters.fuelType));
    }

    if (filters?.ehailingEligible !== undefined) {
      whereConditions.push(
        eq(h_vehicleCatalog.ehailingEligible, filters.ehailingEligible)
      );
    }

    if (filters?.make) {
      whereConditions.push(eq(h_vehicleCatalog.make, filters.make));
    }

    if (filters?.yearFrom) {
      whereConditions.push(
        sql`${h_vehicleCatalog.year} >= ${filters.yearFrom}`
      );
    }

    if (filters?.yearTo) {
      whereConditions.push(sql`${h_vehicleCatalog.year} <= ${filters.yearTo}`);
    }

    // Get catalog items
    const catalogItems = await db
      .select()
      .from(h_vehicleCatalog)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(h_vehicleCatalog.createdAt));

    // Get related data for each catalog item
    const catalogItemsWithRelations = await Promise.all(
      catalogItems.map(async (item) => {
        const [features, platforms, images] = await Promise.all([
          // Get features
          db
            .select({ feature: h_vehicleCatalogFeatures.feature })
            .from(h_vehicleCatalogFeatures)
            .where(eq(h_vehicleCatalogFeatures.catalogId, item.id)),

          // Get platforms
          db
            .select({ platform: h_vehicleCatalogPlatforms.platform })
            .from(h_vehicleCatalogPlatforms)
            .where(eq(h_vehicleCatalogPlatforms.catalogId, item.id)),

          // Get images
          db
            .select({
              id: h_vehicleCatalogImages.id,
              imageUrl: h_vehicleCatalogImages.imageUrl,
              isPrimary: h_vehicleCatalogImages.isPrimary,
            })
            .from(h_vehicleCatalogImages)
            .where(eq(h_vehicleCatalogImages.catalogId, item.id))
            .orderBy(
              desc(h_vehicleCatalogImages.isPrimary),
              asc(h_vehicleCatalogImages.id)
            ),
        ]);

        return {
          ...item,
          features: features.map((f) => f.feature),
          platforms: platforms.map((p) => p.platform),
          images: images,
        };
      })
    );

    return catalogItemsWithRelations;
  } catch (error) {
    console.error("Error fetching vehicle catalog items:", error);
    throw new Error("Failed to fetch vehicle catalog items");
  }
}

/**
 * Get a single vehicle catalog item by ID with all related data
 */
export async function getVehicleCatalogItemById(
  id: number
): Promise<VehicleCatalogItem | null> {
  try {
    const catalogItem = await db
      .select()
      .from(h_vehicleCatalog)
      .where(eq(h_vehicleCatalog.id, id))
      .limit(1);

    if (catalogItem.length === 0) {
      return null;
    }

    const [features, platforms, images] = await Promise.all([
      // Get features
      db
        .select({ feature: h_vehicleCatalogFeatures.feature })
        .from(h_vehicleCatalogFeatures)
        .where(eq(h_vehicleCatalogFeatures.catalogId, id)),

      // Get platforms
      db
        .select({ platform: h_vehicleCatalogPlatforms.platform })
        .from(h_vehicleCatalogPlatforms)
        .where(eq(h_vehicleCatalogPlatforms.catalogId, id)),

      // Get images
      db
        .select({
          id: h_vehicleCatalogImages.id,
          imageUrl: h_vehicleCatalogImages.imageUrl,
          isPrimary: h_vehicleCatalogImages.isPrimary,
        })
        .from(h_vehicleCatalogImages)
        .where(eq(h_vehicleCatalogImages.catalogId, id))
        .orderBy(
          desc(h_vehicleCatalogImages.isPrimary),
          asc(h_vehicleCatalogImages.id)
        ),
    ]);

    return {
      ...catalogItem[0],
      features: features.map((f) => f.feature),
      platforms: platforms.map((p) => p.platform),
      images: images,
    };
  } catch (error) {
    console.error("Error fetching vehicle catalog item:", error);
    throw new Error("Failed to fetch vehicle catalog item");
  }
}

// ==================== CREATE OPERATIONS ====================

/**
 * Create a new vehicle catalog item with all related data
 */
export async function createVehicleCatalogItem(
  data: VehicleCatalogCreateData
): Promise<VehicleCatalogItem> {
  try {
    return await db.transaction(async (tx) => {
      // Create the main catalog item
      const catalogItem = await tx
        .insert(h_vehicleCatalog)
        .values({
          make: data.make,
          model: data.model,
          year: data.year,
          variant: data.variant,
          category: data.category,
          fuelType: data.fuelType,
          ehailingEligible: data.ehailingEligible || false,
          estimatedPrice: data.estimatedPrice,
          weeklyFeeTarget: data.weeklyFeeTarget,
          description: data.description,
          specifications: data.specifications,
        })
        .returning();

      const catalogId = catalogItem[0].id;

      // Insert features
      if (data.features.length > 0) {
        await tx.insert(h_vehicleCatalogFeatures).values(
          data.features.map((feature) => ({
            catalogId,
            feature,
          }))
        );
      }

      // Insert platforms
      if (data.platforms.length > 0) {
        await tx.insert(h_vehicleCatalogPlatforms).values(
          data.platforms.map((platform) => ({
            catalogId,
            platform,
          }))
        );
      }

      // Insert images
      if (data.images.length > 0) {
        await tx.insert(h_vehicleCatalogImages).values(
          data.images.map((image) => ({
            catalogId,
            imageUrl: image.imageUrl,
            isPrimary: image.isPrimary,
          }))
        );
      }

      // Return the complete item
      return {
        ...catalogItem[0],
        features: data.features,
        platforms: data.platforms,
        images: data.images.map((img, index) => ({
          id: index + 1, // Temporary ID for new images
          imageUrl: img.imageUrl,
          isPrimary: img.isPrimary,
        })),
      };
    });
  } catch (error) {
    console.error("Error creating vehicle catalog item:", error);
    throw new Error("Failed to create vehicle catalog item");
  }
}

// ==================== UPDATE OPERATIONS ====================

/**
 * Update a vehicle catalog item with all related data
 */
export async function updateVehicleCatalogItem(
  id: number,
  data: Partial<VehicleCatalogCreateData>
): Promise<VehicleCatalogItem | null> {
  try {
    return await db.transaction(async (tx) => {
      // Check if catalog item exists
      const existingItem = await tx
        .select()
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, id))
        .limit(1);

      if (existingItem.length === 0) {
        return null;
      }

      // Update the main catalog item
      const updatedItem = await tx
        .update(h_vehicleCatalog)
        .set({
          make: data.make,
          model: data.model,
          year: data.year,
          variant: data.variant,
          category: data.category,
          fuelType: data.fuelType,
          ehailingEligible: data.ehailingEligible,
          estimatedPrice: data.estimatedPrice,
          weeklyFeeTarget: data.weeklyFeeTarget,
          description: data.description,
          specifications: data.specifications,
          updatedAt: new Date().toISOString(),
        })
        .where(eq(h_vehicleCatalog.id, id))
        .returning();

      // Update features if provided
      if (data.features !== undefined) {
        // Delete existing features
        await tx
          .delete(h_vehicleCatalogFeatures)
          .where(eq(h_vehicleCatalogFeatures.catalogId, id));

        // Insert new features
        if (data.features.length > 0) {
          await tx.insert(h_vehicleCatalogFeatures).values(
            data.features.map((feature) => ({
              catalogId: id,
              feature,
            }))
          );
        }
      }

      // Update platforms if provided
      if (data.platforms !== undefined) {
        // Delete existing platforms
        await tx
          .delete(h_vehicleCatalogPlatforms)
          .where(eq(h_vehicleCatalogPlatforms.catalogId, id));

        // Insert new platforms
        if (data.platforms.length > 0) {
          await tx.insert(h_vehicleCatalogPlatforms).values(
            data.platforms.map((platform) => ({
              catalogId: id,
              platform,
            }))
          );
        }
      }

      // Update images if provided
      if (data.images !== undefined) {
        // Delete existing images
        await tx
          .delete(h_vehicleCatalogImages)
          .where(eq(h_vehicleCatalogImages.catalogId, id));

        // Insert new images
        if (data.images.length > 0) {
          await tx.insert(h_vehicleCatalogImages).values(
            data.images.map((image) => ({
              catalogId: id,
              imageUrl: image.imageUrl,
              isPrimary: image.isPrimary,
            }))
          );
        }
      }

      // Get the updated item with all relations
      return await getVehicleCatalogItemById(id);
    });
  } catch (error) {
    console.error("Error updating vehicle catalog item:", error);
    throw new Error("Failed to update vehicle catalog item");
  }
}

// ==================== DELETE OPERATIONS ====================

/**
 * Delete a vehicle catalog item and all related data
 */
export async function deleteVehicleCatalogItem(id: number): Promise<boolean> {
  try {
    return await db.transaction(async (tx) => {
      // Check if catalog item exists
      const existingItem = await tx
        .select()
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, id))
        .limit(1);

      if (existingItem.length === 0) {
        return false;
      }

      // Delete related data (cascade should handle this, but being explicit)
      await tx
        .delete(h_vehicleCatalogFeatures)
        .where(eq(h_vehicleCatalogFeatures.catalogId, id));

      await tx
        .delete(h_vehicleCatalogPlatforms)
        .where(eq(h_vehicleCatalogPlatforms.catalogId, id));

      await tx
        .delete(h_vehicleCatalogImages)
        .where(eq(h_vehicleCatalogImages.catalogId, id));

      // Delete the main catalog item
      await tx.delete(h_vehicleCatalog).where(eq(h_vehicleCatalog.id, id));

      return true;
    });
  } catch (error) {
    console.error("Error deleting vehicle catalog item:", error);
    throw new Error("Failed to delete vehicle catalog item");
  }
}

// ==================== PUBLISH TO LISTINGS ====================

/**
 * Publish a catalog item to h_listings table
 */
export async function publishCatalogItemToListings(
  catalogId: number,
  listingDetails: {
    rate?: number;
    type?: string;
    initiationFee?: number;
    driverExperienceRequired?: number;
    minimumAge?: number;
    preferredGender?: string;
    preferredLocation?: string;
  } = {}
): Promise<{ success: boolean; listingId?: number; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];

    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    return await db.transaction(async (tx) => {
      // Verify catalog item exists
      const catalogItem = await tx
        .select()
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, catalogId))
        .limit(1);

      if (catalogItem.length === 0) {
        return { success: false, error: "Catalog item not found" };
      }

      // Check if already published
      const existingListing = await tx
        .select()
        .from(h_listings)
        .where(
          and(
            eq(h_listings.sourceType, "catalog"),
            eq(h_listings.sourceId, catalogId)
          )
        )
        .limit(1);

      if (existingListing.length > 0) {
        return { success: false, error: "Catalog item already published" };
      }

      // Prepare listing details
      const defaultListingDetails = {
        rate: listingDetails.rate || catalogItem[0].weeklyFeeTarget || 2700,
        type: listingDetails.type || "weekly",
        initiationFee: listingDetails.initiationFee || 7500,
        driverExperienceRequired: listingDetails.driverExperienceRequired || 1,
        minimumAge: listingDetails.minimumAge || 21,
        preferredGender: listingDetails.preferredGender || null,
        preferredLocation: listingDetails.preferredLocation || null,
      };

      // Create the listing
      const newListing = await tx
        .insert(h_listings)
        .values({
          partyId: parseInt(partyId),
          sourceType: "catalog",
          sourceId: catalogId,
          listingType: "ehailing-platform",
          effectiveFrom: new Date().toISOString(),
          effectiveTo: new Date(
            Date.now() + 90 * 24 * 60 * 60 * 1000
          ).toISOString(), // 90 days
          listingDetails: JSON.stringify(defaultListingDetails),
        })
        .returning();

      // Create initial approval status (approved since it's admin)
      await tx.insert(h_listing_approval_status).values({
        listingId: newListing[0].id,
        status: "approved",
        statusAt: new Date().toISOString(),
        statusBy: parseInt(partyId),
      });

      // Create initial publish status (published)
      await tx.insert(h_listing_publish_status).values({
        listingId: newListing[0].id,
        status: "published",
        statusAt: new Date().toISOString(),
        statusBy: parseInt(partyId),
      });

      return { success: true, listingId: newListing[0].id };
    });
  } catch (error) {
    console.error("Error publishing catalog item to listings:", error);
    return { success: false, error: "Failed to publish catalog item" };
  }
}

// ==================== UTILITY FUNCTIONS ====================

/**
 * Get unique values for filtering
 */
export async function getVehicleCatalogFilterOptions(): Promise<{
  categories: string[];
  fuelTypes: string[];
  makes: string[];
  yearRange: { min: number; max: number };
}> {
  try {
    const [categories, fuelTypes, makes, yearRange] = await Promise.all([
      // Get unique categories
      db
        .selectDistinct({ category: h_vehicleCatalog.category })
        .from(h_vehicleCatalog)
        .orderBy(asc(h_vehicleCatalog.category)),

      // Get unique fuel types
      db
        .selectDistinct({ fuelType: h_vehicleCatalog.fuelType })
        .from(h_vehicleCatalog)
        .orderBy(asc(h_vehicleCatalog.fuelType)),

      // Get unique makes
      db
        .selectDistinct({ make: h_vehicleCatalog.make })
        .from(h_vehicleCatalog)
        .orderBy(asc(h_vehicleCatalog.make)),

      // Get year range
      db
        .select({
          min: sql<number>`MIN(${h_vehicleCatalog.year})`,
          max: sql<number>`MAX(${h_vehicleCatalog.year})`,
        })
        .from(h_vehicleCatalog),
    ]);

    return {
      categories: categories.map((c) => c.category),
      fuelTypes: fuelTypes.map((f) => f.fuelType),
      makes: makes.map((m) => m.make),
      yearRange: yearRange[0] || { min: 2020, max: new Date().getFullYear() },
    };
  } catch (error) {
    console.error("Error fetching filter options:", error);
    throw new Error("Failed to fetch filter options");
  }
}
