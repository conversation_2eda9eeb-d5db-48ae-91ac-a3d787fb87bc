"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, CheckCircle, AlertCircle, Car, Users, Shield, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import PageWithBottomNav from "@/components/PageWithBottomNav";
import { 
  addVehicleToGroup,
  getVehicleMakes,
  getVehicleModelsByMake,
  getCompanies
} from "@/drizzle-actions/vehicle-groups";
import { OWNERSHIP_DISCLAIMER } from "@/types/vehicle-groups";
import type { 
  VehicleToGroupRequest, 
} from "@/types/vehicle-groups";
import type { CompanyRead } from "@/types/company";
import type { VehicleMakeRead } from "@/types/vehicle-makes";
import type { VehicleModelReadWithMake } from "@/types/vehicle-model";

interface VehicleToGroupFormProps {
  currentUserPartyId: number;
  preselectedCompanyId?: number;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
}

function VehicleToGroupFormContent({
  currentUserPartyId,
  preselectedCompanyId,
  onSuccess,
  onCancel
}: VehicleToGroupFormProps) {
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    vin_number: "",
    make_id: "",
    model_id: "",
    vehicle_registration: "",
    country_of_registration: "",
    manufacturing_year: "",
    purchase_date: "",
    color: "",
    company_id: preselectedCompanyId ? preselectedCompanyId.toString() : "",
    ownership_disclaimer_accepted: false,
    ownership_confirmation: false,
    owner_statement: ""
  });

  const [companies, setCompanies] = useState<CompanyRead[]>([]);
  const [makes, setMakes] = useState<VehicleMakeRead[]>([]);
  const [models, setModels] = useState<VehicleModelReadWithMake[]>([]);
  
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Load companies and makes on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [companiesData, makesData] = await Promise.all([
          getCompanies(),
          getVehicleMakes()
        ]);
        setCompanies(companiesData);
        setMakes(makesData);
      } catch (error) {
        console.error("Error loading initial data:", error);
        setError("Failed to load form data");
      }
    };

    loadInitialData();
  }, []);

  // Load models when make changes
  useEffect(() => {
    const loadModels = async () => {
      if (formData.make_id) {
        try {
          const modelsData = await getVehicleModelsByMake(parseInt(formData.make_id));
          setModels(modelsData);
        } catch (error) {
          console.error("Error loading models:", error);
          setModels([]);
        }
      } else {
        setModels([]);
      }
    };

    loadModels();
  }, [formData.make_id]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear model selection when make changes
    if (name === "make_id") {
      setFormData(prev => ({
        ...prev,
        model_id: ""
      }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.company_id) {
      setError("Please select a group");
      return;
    }

    if (!formData.ownership_disclaimer_accepted || !formData.ownership_confirmation) {
      setError("You must accept the ownership disclaimer and confirm ownership");
      return;
    }

    if (!formData.owner_statement.trim()) {
      setError("Please provide an ownership statement");
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const request: VehicleToGroupRequest = {
        company_id: parseInt(formData.company_id),
        vin_number: formData.vin_number,
        make_id: parseInt(formData.make_id),
        model_id: parseInt(formData.model_id),
        vehicle_registration: formData.vehicle_registration || undefined,
        country_of_registration: formData.country_of_registration || undefined,
        manufacturing_year: formData.manufacturing_year ? parseInt(formData.manufacturing_year) : undefined,
        purchase_date: formData.purchase_date || undefined,
        color: formData.color || undefined,
        ownership_disclaimer_accepted: formData.ownership_disclaimer_accepted,
        ownership_confirmation: formData.ownership_confirmation,
        owner_statement: formData.owner_statement
      };

      const result = await addVehicleToGroup(request);

      if (result.success) {
        setSuccess(true);
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error("Submit error:", error);
      setError("Failed to add vehicle to group");
    } finally {
      setSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-[#f5f5f5]">
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button className="mr-4" onClick={onCancel || (() => router.back())}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Add Vehicle to Group</h1>
        </div>

        {/* Success Content */}
        <div className="p-6 flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <CheckCircle className="mx-auto mb-4 h-16 w-16 text-[#009639]" />
              <h2 className="text-2xl font-bold text-[#009639] mb-2">Success!</h2>
              <p className="text-[#333333] mb-6">
                Your vehicle has been successfully added to the group.
              </p>
              <Button 
                onClick={onCancel || (() => router.back())} 
                className="bg-[#009639] hover:bg-[#007A2F] w-full"
              >
                Continue
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={onCancel || (() => router.back())}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Add Vehicle to Group</h1>
      </div>

      {/* Form Content */}
      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Vehicle Information */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                <Car className="h-5 w-5 text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* VIN Number */}
              <div>
                <Label htmlFor="vin_number" className="text-sm text-[#333333] font-medium">
                  VIN Number *
                </Label>
                <Input
                  id="vin_number"
                  name="vin_number"
                  value={formData.vin_number}
                  onChange={handleInputChange}
                  placeholder="Enter VIN number"
                  className="mt-1 uppercase border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  maxLength={17}
                  required
                />
              </div>

              {/* Make and Model */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="make_id" className="text-sm text-[#333333] font-medium">
                    Make *
                  </Label>
                  <Select
                    value={formData.make_id}
                    onValueChange={(value) => handleSelectChange("make_id", value)}
                    required
                  >
                    <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                      <SelectValue placeholder="Select make" />
                    </SelectTrigger>
                    <SelectContent>
                      {makes.map((make) => (
                        <SelectItem key={make.id} value={make.id.toString()}>
                          {make.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="model_id" className="text-sm text-[#333333] font-medium">
                    Model *
                  </Label>
                  <Select
                    value={formData.model_id}
                    onValueChange={(value) => handleSelectChange("model_id", value)}
                    disabled={!formData.make_id}
                    required
                  >
                    <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      {models.map((model) => (
                        <SelectItem key={model.id} value={model.id.toString()}>
                          {model.model} ({model.year_model})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Registration and Country */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vehicle_registration" className="text-sm text-[#333333] font-medium">
                    Registration Number
                  </Label>
                  <Input
                    id="vehicle_registration"
                    name="vehicle_registration"
                    value={formData.vehicle_registration}
                    onChange={handleInputChange}
                    placeholder="e.g., ABC123GP"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>

                <div>
                  <Label htmlFor="country_of_registration" className="text-sm text-[#333333] font-medium">
                    Country of Registration
                  </Label>
                  <Input
                    id="country_of_registration"
                    name="country_of_registration"
                    value={formData.country_of_registration}
                    onChange={handleInputChange}
                    placeholder="e.g., South Africa"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>
              </div>

              {/* Year and Color */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="manufacturing_year" className="text-sm text-[#333333] font-medium">
                    Manufacturing Year
                  </Label>
                  <Input
                    id="manufacturing_year"
                    name="manufacturing_year"
                    type="number"
                    value={formData.manufacturing_year}
                    onChange={handleInputChange}
                    placeholder="e.g., 2020"
                    min="1900"
                    max={new Date().getFullYear() + 1}
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>

                <div>
                  <Label htmlFor="color" className="text-sm text-[#333333] font-medium">
                    Color
                  </Label>
                  <Input
                    id="color"
                    name="color"
                    value={formData.color}
                    onChange={handleInputChange}
                    placeholder="e.g., White, Black"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>
              </div>

              {/* Purchase Date */}
              <div>
                <Label htmlFor="purchase_date" className="text-sm text-[#333333] font-medium">
                  Purchase Date
                </Label>
                <Input
                  id="purchase_date"
                  name="purchase_date"
                  type="date"
                  value={formData.purchase_date}
                  onChange={handleInputChange}
                  className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Group Selection */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                <Users className="h-5 w-5 text-[#009639]" />
                Select Group
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="company_id" className="text-sm text-[#333333] font-medium">
                  Group *
                </Label>
                <Select
                  value={formData.company_id}
                  onValueChange={(value) => handleSelectChange("company_id", value)}
                  disabled={!!preselectedCompanyId}
                  required
                >
                  <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                    <SelectValue placeholder="Select a group" />
                  </SelectTrigger>
                  <SelectContent>
                    {companies.map((company) => (
                      <SelectItem key={company.id} value={company.id.toString()}>
                        {company.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Ownership Disclaimer */}
          {formData.company_id && (
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                  <Shield className="h-5 w-5 text-[#009639]" />
                  {OWNERSHIP_DISCLAIMER.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Disclaimer Content */}
                <Card className="bg-yellow-50 border-yellow-200">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {OWNERSHIP_DISCLAIMER.content.map((item, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <span className="text-yellow-600 font-bold mt-1">•</span>
                          <p className="text-sm text-[#333333]">{item}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Checkboxes */}
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="ownership_disclaimer"
                      checked={formData.ownership_disclaimer_accepted}
                      onCheckedChange={(checked) => 
                        handleCheckboxChange("ownership_disclaimer_accepted", checked as boolean)
                      }
                    />
                    <Label htmlFor="ownership_disclaimer" className="text-sm text-[#333333] leading-5">
                      I have read and accept the ownership disclaimer and co-ownership agreement terms *
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="ownership_confirmation"
                      checked={formData.ownership_confirmation}
                      onCheckedChange={(checked) => 
                        handleCheckboxChange("ownership_confirmation", checked as boolean)
                      }
                    />
                    <Label htmlFor="ownership_confirmation" className="text-sm text-[#333333] leading-5">
                      I confirm that I am the legal owner of this vehicle *
                    </Label>
                  </div>
                </div>

                {/* Ownership Statement */}
                <div>
                  <Label htmlFor="owner_statement" className="text-sm text-[#333333] font-medium">
                    Ownership Statement *
                  </Label>
                  <Textarea
                    id="owner_statement"
                    name="owner_statement"
                    value={formData.owner_statement}
                    onChange={handleInputChange}
                    placeholder="Please write a brief statement confirming your ownership of this vehicle and your intent to enter into a co-ownership agreement..."
                    className="mt-1 min-h-20 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                    required
                  />
                  <p className="text-xs text-[#797879] mt-1">
                    This statement will be recorded for legal purposes.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="border-red-500 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            {onCancel && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                className="order-2 sm:order-1 border-gray-200 text-[#333333] hover:bg-gray-50"
              >
                Cancel
              </Button>
            )}
            
            <Button
              type="submit"
              disabled={
                !formData.vin_number ||
                !formData.make_id ||
                !formData.model_id ||
                !formData.company_id ||
                !formData.ownership_disclaimer_accepted ||
                !formData.ownership_confirmation ||
                !formData.owner_statement.trim() ||
                submitting
              }
              className="order-1 sm:order-2 bg-[#009639] hover:bg-[#007A2F] flex-1 sm:flex-none sm:min-w-[200px]"
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding Vehicle...
                </>
              ) : (
                "Add Vehicle to Group"
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function VehicleToGroupForm(props: VehicleToGroupFormProps) {
  return (
    <PageWithBottomNav>
      <VehicleToGroupFormContent {...props} />
    </PageWithBottomNav>
  );
} 