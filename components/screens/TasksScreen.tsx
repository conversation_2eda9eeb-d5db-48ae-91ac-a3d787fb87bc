"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON>S<PERSON>re, 
  AlertCircle, 
  UserPlus, 
  FileText, 
  Wrench, 
  CreditCard, 
  Shield,
  CheckCircle,
  X,
  Calendar,
  Timer,
  ChevronRight,
  ArrowLeft,
  Car
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { useNavigation } from "@/hooks/useNavigation";

// Task Types
type TaskType = 
  | 'onboarding'
  | 'compliance'
  | 'invitations'
  | 'approvals'
  | 'maintenance'
  | 'financial'
  | 'handover'

type TaskPriority = 
  | 'blocking'
  | 'urgent'
  | 'normal'
  | 'optional'

type TaskStatus = 
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'dismissed'
  | 'expired'

interface UserTask {
  id: string
  userId: string
  type: TaskType
  priority: TaskPriority
  status: TaskStatus
  title: string
  description: string
  actionText: string
  completionText?: string
  dueDate?: Date
  estimatedMinutes?: number
  metadata: {
    groupId?: string
    vehicleId?: string
    documentType?: string
    paymentId?: string
    termsVersion?: string
    relatedEntityId?: string
    completionPercentage?: number
    inviterName?: string
    paymentType?: string
    // Handover-related fields
    handoverId?: string
    handoverType?: string
    scheduledTime?: Date
    fromPartyId?: string
    toPartyId?: string
    bookingReference?: string
    isInspection?: boolean
    vehicleDetails?: {
      make: string
      model: string
      year: number
      registration: string
      color: string
    }
    location?: string
    inspectionType?: string
  }
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dismissedAt?: Date
  expiresAt?: Date
}

type EmptyStateType = 
  | 'all_complete'
  | 'loading'
  | 'error'
  | 'filtered_empty'
  | 'completed_only'
  | 'no_blocking'

export default function TasksScreen() {
  const { goBack, navigateToTaskDetail } = useNavigation();
  const [tasks, setTasks] = useState<UserTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | TaskPriority>('all');
  const [showCompleted, setShowCompleted] = useState(false);
  
  // Debug: Party switcher for testing handover perspectives
  const [currentPartyId, setCurrentPartyId] = useState('user_123');
  const [inspectionMode, setInspectionMode] = useState(true);
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const partyOptions = [
    { id: 'user_123', name: 'Party A (Receiver)' },
    { id: 'user_456', name: 'Party B (Giver)' }
  ];

  // Mock data for comprehensive testing
  const mockTasks: UserTask[] = [
    // Blocking tasks (user_123 perspective)
    {
      id: '1',
      userId: 'user_123',
      type: 'compliance',
      priority: 'blocking',
      status: 'pending',
      title: 'Accept Updated Terms & Conditions',
      description: 'Review and accept updated terms to continue using Poolly.',
      actionText: 'Review & Accept Terms',
      estimatedMinutes: 5,
      metadata: { termsVersion: 'v2.1' },
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: '2',
      userId: 'user_123',
      type: 'onboarding',
      priority: 'blocking', 
      status: 'pending',
      title: 'Complete Profile Setup',
      description: 'Add required information to activate your account.',
      actionText: 'Complete Profile',
      estimatedMinutes: 10,
      metadata: { completionPercentage: 60 },
      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000)
    },
    // Handover tasks - user_123 (receiving vehicle)
    {
      id: '7',
      userId: 'user_123',
      type: 'handover',
      priority: 'urgent',
      status: 'pending',
      title: 'Vehicle Pickup Tomorrow',
      description: 'Tesla Model 3 pickup scheduled for tomorrow at 10:00 AM.',
      actionText: 'View Details',
      estimatedMinutes: 30,
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      metadata: { 
        vehicleId: 'vehicle_456',
        handoverId: '123',
        handoverType: 'BOOKING_START',
        scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        fromPartyId: 'user_789',
        toPartyId: 'user_123',
        bookingReference: 'BK-2024-001',
        isInspection: inspectionMode,
        vehicleDetails: {
          make: 'Tesla',
          model: 'Model 3',
          year: 2023,
          registration: 'ABC123',
          color: 'White'
        },
        location: '123 Main St, Toronto, ON'
      },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: '8',
      userId: 'user_123',
      type: 'handover',
      priority: 'blocking',
      status: 'pending',
      title: inspectionMode ? 'Complete Vehicle Inspection' : 'Acknowledge Vehicle Pickup',
      description: inspectionMode 
        ? 'Your pickup time has arrived. Complete the vehicle inspection before taking possession.'
        : 'Confirm your vehicle pickup appointment.',
      actionText: inspectionMode ? 'Start Inspection' : 'Acknowledge Pickup',
      estimatedMinutes: inspectionMode ? 15 : 5,
      dueDate: new Date(), // Now
      metadata: { 
        vehicleId: 'vehicle_457',
        handoverId: '124',
        handoverType: 'BOOKING_START',
        scheduledTime: new Date(),
        fromPartyId: 'user_789',
        toPartyId: 'user_123',
        bookingReference: 'BK-2024-002',
        isInspection: inspectionMode,
        inspectionType: inspectionMode ? 'PRE_HANDOVER' : undefined,
        vehicleDetails: {
          make: 'BMW',
          model: 'X3',
          year: 2022,
          registration: 'DEF456',
          color: 'Black'
        },
        location: '456 Oak Ave, Toronto, ON'
      },
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    // Handover tasks - user_456 (returning vehicle)
    {
      id: '9',
      userId: 'user_456',
      type: 'handover',
      priority: 'urgent',
      status: 'pending',
      title: 'Vehicle Return Due Soon',
      description: 'Audi A4 return scheduled in 2 hours. Please prepare for handover.',
      actionText: 'View Return Details',
      estimatedMinutes: 20,
      dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000), // In 2 hours
      metadata: { 
        vehicleId: 'vehicle_458',
        handoverId: '125',
        handoverType: 'BOOKING_END',
        scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
        fromPartyId: 'user_456',
        toPartyId: 'user_789',
        bookingReference: 'BK-2024-003',
        isInspection: inspectionMode,
        vehicleDetails: {
          make: 'Audi',
          model: 'A4',
          year: 2021,
          registration: 'GHI789',
          color: 'Silver'
        },
        location: '789 Pine St, Toronto, ON'
      },
      createdAt: new Date(Date.now() - 30 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: '10',
      userId: 'user_456',
      type: 'handover',
      priority: 'blocking',
      status: 'pending',
      title: inspectionMode ? 'Complete Return Inspection' : 'Acknowledge Vehicle Return',
      description: inspectionMode 
        ? 'Time to return the vehicle. Please complete the return inspection process.'
        : 'Time to return the vehicle. Please acknowledge the handover.',
      actionText: inspectionMode ? 'Start Return Inspection' : 'Acknowledge Return',
      estimatedMinutes: inspectionMode ? 15 : 5,
      dueDate: new Date(),
      metadata: { 
        vehicleId: 'vehicle_458',
        handoverId: '126',
        handoverType: 'BOOKING_END',
        scheduledTime: new Date(),
        fromPartyId: 'user_456',
        toPartyId: 'user_789',
        bookingReference: 'BK-2024-004',
        isInspection: inspectionMode,
        inspectionType: inspectionMode ? 'POST_HANDOVER' : undefined,
        vehicleDetails: {
          make: 'Mercedes',
          model: 'C-Class',
          year: 2023,
          registration: 'JKL012',
          color: 'Blue'
        },
        location: '321 Elm St, Toronto, ON'
      },
      createdAt: new Date(Date.now() - 15 * 60 * 1000),
      updatedAt: new Date(Date.now() - 15 * 60 * 1000)
    },
    // Regular tasks for context
    {
      id: '3',
      userId: 'user_123',
      type: 'invitations',
      priority: 'urgent',
      status: 'pending',
      title: 'Group Invitation from Sarah',
      description: 'Sarah invited you to join "Weekend Warriors" co-ownership group.',
      actionText: 'View Invitation',
      estimatedMinutes: 3,
      metadata: { inviterName: 'Sarah', groupId: 'group_456' },
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: '4',
      userId: 'user_123',
      type: 'maintenance',
      priority: 'normal',
      status: 'pending',
      title: 'Schedule Vehicle Inspection',
      description: 'Toyota Camry requires annual inspection.',
      actionText: 'Schedule Inspection',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      metadata: { vehicleId: 'vehicle_456' },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: '5',
      userId: 'user_123',
      type: 'financial',
      priority: 'optional',
      status: 'pending',
      title: 'Set Up Direct Debit',
      description: 'Automate your monthly contributions for convenience.',
      actionText: 'Set Up Payment',
      estimatedMinutes: 3,
      metadata: { paymentType: 'direct_debit' },
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    },
    // Handover tasks - aligned with TaskDetailScreen mock data
    {
      id: '6',
      userId: 'user_123',
      type: 'handover',
      priority: 'urgent',
      status: 'pending',
      title: 'Vehicle Handover Reminder',
      description: 'You have a vehicle handover scheduled for tomorrow at 10:00 AM. Please prepare for the vehicle inspection.',
      actionText: 'View Details',
      estimatedMinutes: 30,
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      metadata: { 
        handoverId: '123',
        vehicleId: '456',
        handoverType: 'BOOKING_START',
        scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        fromPartyId: '789',
        toPartyId: 'user_123',
        vehicleDetails: {
          make: 'Tesla',
          model: 'Model 3',
          year: 2023,
          registration: 'ABC123',
          color: 'White'
        },
        location: '123 Main St, Toronto, ON',
        isInspection: false, // Reminder mode
        bookingReference: 'BK-2024-001'
      },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: '7',
      userId: 'user_456',
      type: 'handover',
      priority: 'blocking',
      status: 'pending',
      title: 'Complete Vehicle Inspection',
      description: 'Your handover time has arrived. Please complete the vehicle inspection before taking possession.',
      actionText: 'Start Inspection',
      estimatedMinutes: 15,
      dueDate: new Date(), // Now
      metadata: { 
        handoverId: '124',
        vehicleId: '457',
        handoverType: 'BOOKING_START',
        scheduledTime: new Date(),
        fromPartyId: '790',
        toPartyId: 'user_456',
        vehicleDetails: {
          make: 'BMW',
          model: 'X3',
          year: 2022,
          registration: 'DEF456',
          color: 'Black'
        },
        location: '456 Oak Ave, Toronto, ON',
        isInspection: true, // Inspection mode
        bookingReference: 'BK-2024-002',
        inspectionType: 'PRE_HANDOVER'
      },
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: '8',
      userId: 'user_789',
      type: 'handover',
      priority: 'urgent',
      status: 'pending',
      title: 'Acknowledge Vehicle Return',
      description: 'A vehicle return has been requested. Please confirm the handover and complete the return inspection.',
      actionText: 'Acknowledge Return',
      estimatedMinutes: 20,
      dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000), // In 2 hours
      metadata: { 
        handoverId: '125',
        vehicleId: '458',
        handoverType: 'BOOKING_END',
        scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
        fromPartyId: 'user_456',
        toPartyId: 'user_789',
        vehicleDetails: {
          make: 'Audi',
          model: 'A4',
          year: 2021,
          registration: 'GHI789',
          color: 'Silver'
        },
        location: '789 Pine St, Toronto, ON',
        isInspection: false, // Acknowledgment mode first
        bookingReference: 'BK-2024-003'
      },
      createdAt: new Date(Date.now() - 30 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000)  
    },
    // Completed task
    {
      id: '9',
      userId: 'user_123',
      type: 'compliance',
      priority: 'blocking',
      status: 'completed',
      title: 'Verify Phone Number',
      description: 'Confirm your phone number for security.',
      actionText: 'Verify Phone',
      completionText: 'Phone number verified',
      completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      metadata: {},
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    }
  ];

  // Load mock data
  useEffect(() => {
    const loadTasks = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Filter tasks for current party and update userId
      const partyTasks = mockTasks
        .filter(task => task.userId === currentPartyId)
        .map(task => ({ ...task, userId: currentPartyId }));
      
      setTasks(partyTasks);
      setLoading(false);
    };

    loadTasks();
  }, [currentPartyId, inspectionMode]); // Reload when party or inspection mode changes

  // Filter and sort tasks
  const getFilteredTasks = () => {
    let filteredTasks = tasks.filter(task => {
      if (!showCompleted && task.status === 'completed') return false;
      if (filter !== 'all' && task.priority !== filter) return false;
      return true;
    });

    const priorityOrder = { blocking: 0, urgent: 1, normal: 2, optional: 3 };
    return filteredTasks.sort((a, b) => {
      if (a.status === 'completed' && b.status !== 'completed') return 1;
      if (b.status === 'completed' && a.status !== 'completed') return -1;
      
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  };

  const filteredTasks = getFilteredTasks();
  const blockingTasks = tasks.filter(task => task.priority === 'blocking' && task.status === 'pending');
  const pendingTasks = tasks.filter(task => task.status === 'pending');

  // Task action handlers
  const handleTaskAction = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    console.log(`Navigating to task detail: ${task.title}`);
    navigateToTaskDetail(taskId);
  };

  const handleTaskDismiss = (taskId: string) => {
    setTasks(prevTasks =>
      prevTasks.map(t =>
        t.id === taskId
          ? {
              ...t,
              status: 'dismissed' as TaskStatus,
              dismissedAt: new Date()
            }
          : t
      )
    );
  };

  // Get task icon
  const getTaskIcon = (type: TaskType, priority: TaskPriority) => {
    const iconProps = {
      size: 20,
      className: priority === 'blocking' ? 'text-[#009639]' : 
                priority === 'urgent' ? 'text-[#009639]' :
                priority === 'normal' ? 'text-[#009639]' : 'text-[#009639]'
    };

    switch (type) {
      case 'onboarding':
        return <UserPlus {...iconProps} />;
      case 'compliance':
        return <Shield {...iconProps} />;
      case 'invitations':
        return <UserPlus {...iconProps} />;
      case 'approvals':
        return <CheckSquare {...iconProps} />;
      case 'maintenance':
        return <Wrench {...iconProps} />;
      case 'financial':
        return <CreditCard {...iconProps} />;
      case 'handover':
        return <Car {...iconProps} />;
      default:
        return <FileText {...iconProps} />;
    }
  };

  const getPriorityBadge = (priority: TaskPriority) => {
    const variants = {
      blocking: { variant: "destructive" as const, text: "Required", className: "" },
      urgent: { variant: "default" as const, text: "Urgent", className: "bg-orange-500" },
      normal: { variant: "secondary" as const, text: "Normal", className: "" },
      optional: { variant: "outline" as const, text: "Optional", className: "" }
    };

    const config = variants[priority];
    return (
      <Badge 
        variant={config.variant} 
        className={config.className}
      >
        {config.text}
      </Badge>
    );
  };

  const formatTimeEstimate = (minutes?: number) => {
    if (!minutes) return null;
    return minutes < 60 ? `${minutes} min` : `${Math.round(minutes / 60)}h ${minutes % 60}m`;
  };

  const formatDueDate = (dueDate?: Date) => {
    if (!dueDate) return null;
    const now = new Date();
    const diffInDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return "Due today";
    if (diffInDays === 1) return "Due tomorrow";
    if (diffInDays > 1) return `Due in ${diffInDays} days`;
    return "Overdue";
  };

  // Empty states
  const renderEmptyState = (type: EmptyStateType) => {
    const states = {
      loading: (
        <div className="flex flex-col items-center justify-center py-12 px-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mb-4"></div>
          <p className="text-[#797879] text-center">Loading your tasks...</p>
        </div>
      ),
      all_complete: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <CheckCircle size={48} className="text-[#009639] mb-4" />
          <h3 className="text-xl font-semibold text-[#333333] mb-2">All caught up! 🎉</h3>
          <p className="text-[#797879]">No actions needed right now.</p>
          <p className="text-[#797879]">We'll notify you when new tasks arrive.</p>
        </div>
      ),
      no_blocking: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <CheckSquare size={48} className="text-[#009639] mb-4" />
          <h3 className="text-xl font-semibold text-[#333333] mb-2">All required actions complete ✓</h3>
          <p className="text-[#797879]">You have {pendingTasks.length} optional tasks you can tackle when ready.</p>
        </div>
      ),
      completed_only: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <CheckCircle size={48} className="text-[#009639] mb-4" />
          <h3 className="text-xl font-semibold text-[#333333] mb-2">Great work! 🌟</h3>
          <p className="text-[#797879]">All current tasks completed.</p>
        </div>
      ),
      filtered_empty: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <AlertCircle size={48} className="text-[#797879] mb-4" />
          <h3 className="text-lg font-semibold text-[#333333] mb-2">No tasks match your filters</h3>
          <p className="text-[#797879] mb-4">Try adjusting your filters or showing all tasks.</p>
          <Button 
            variant="outline"
            onClick={() => setFilter('all')}
          >
            Show all tasks
          </Button>
        </div>
      ),
      error: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <AlertCircle size={48} className="text-red-500 mb-4" />
          <h3 className="text-lg font-semibold text-[#333333] mb-2">Unable to load tasks</h3>
          <p className="text-[#797879] mb-4">Please try again or contact support if the problem persists.</p>
        </div>
      )
    };

    return states[type];
  };

  // Determine which empty state to show
  const getEmptyStateType = (): EmptyStateType | null => {
    if (loading) return 'loading';
    if (tasks.length === 0) return 'all_complete';
    if (filteredTasks.length === 0) {
      if (filter !== 'all') return 'filtered_empty';
      if (!showCompleted && tasks.every(t => t.status === 'completed')) return 'completed_only';
    }
    if (blockingTasks.length === 0 && pendingTasks.length > 0) return 'no_blocking';
    return null;
  };

  const emptyStateType = getEmptyStateType();

  // Header component
  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Tasks</h1>
      </div>
      
    </div>
  );

  // Filters/tabs component
  const tabs = (
    <div className="bg-white px-4 py-3 border-b border-[#f2f2f2] overflow-x-auto">
      <div className="flex space-x-2">
        <button
          className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
            showCompleted
              ? "bg-[#009639] text-white"
              : "bg-[#f2f2f2] text-[#333333]"
          }`}
          onClick={() => setShowCompleted(!showCompleted)}
        >
          Completed
        </button>
        {[
          { key: 'all', label: 'All Tasks' },
          { key: 'blocking', label: 'Required' },
          { key: 'urgent', label: 'Urgent' },
          { key: 'normal', label: 'Normal' },
          { key: 'optional', label: 'Optional' }
        ].map(({ key, label }) => (
          <button
            key={key}
            className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
              filter === key
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setFilter(key as TaskPriority | 'all')}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );

  if (emptyStateType) {
    return (
      <PageWithScroll
        header={header}
        tabs={tabs}
        className="bg-[#f5f5f5]"
        paddingBottom="pb-32"
      >
        {renderEmptyState(emptyStateType)}
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll
      header={header}
      tabs={tabs}
      className="bg-[#f5f5f5]"
      paddingBottom="pb-32"
    >
      {/* Debug Party Switcher */}
      {showDebugPanel && (
      <div className="p-4 bg-white border-b border-gray-200 shadow-sm">
        <h3 className="text-sm font-semibold text-gray-700 mb-3">🔧 Testing Mode</h3>
        
        {/* Party Switcher */}
        <div className="mb-3">
          <span className="text-xs text-gray-600 mb-2 block">Party Perspective:</span>
          <div className="flex gap-3">
            {partyOptions.map(option => (
              <label key={option.id} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="radio"
                  name="party"
                  value={option.id}
                  checked={currentPartyId === option.id}
                  onChange={(e) => setCurrentPartyId(e.target.value)}
                  className="w-3 h-3 text-[#009639] focus:ring-[#009639] focus:ring-1"
                />
                <span className="text-xs text-gray-700">{option.name}</span>
              </label>
            ))}
          </div>
        </div>
        
        {/* Inspection Mode Switcher */}
        <div className="mb-2">
          <span className="text-xs text-gray-600 mb-2 block">Handover Mode:</span>
          <div className="flex gap-3">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="inspectionMode"
                value="false"
                checked={!inspectionMode}
                onChange={() => setInspectionMode(false)}
                className="w-3 h-3 text-[#009639] focus:ring-[#009639] focus:ring-1"
              />
              <span className="text-xs text-gray-700">Reminder Only</span>
            </label>
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="inspectionMode"
                value="true"
                checked={inspectionMode}
                onChange={() => setInspectionMode(true)}
                className="w-3 h-3 text-[#009639] focus:ring-[#009639] focus:ring-1"
              />
              <span className="text-xs text-gray-700">Full Inspection</span>
            </label>
          </div>
        </div>
        
        <p className="text-xs text-gray-500">
          Switch perspectives and modes to test handover scenarios.
        </p>
      </div>
      )}

      {/* Summary Section */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-[#333333]">Task Overview</h2>
            <div className="flex items-center gap-2">
              {blockingTasks.length > 0 && (
                <Badge className="bg-white text-[#009639] border border-[#009639]">
                  {blockingTasks.length} Required
                </Badge>
              )}
              <Badge className="bg-white text-[#009639] border border-[#009639]">
                {pendingTasks.length} Pending
              </Badge>
            </div>
          </div>
          
          {blockingTasks.length > 0 && (
            <div className="bg-[#009639] border border-[#009639] rounded-lg p-3 mb-3">
              <div className="flex items-center gap-2 mb-1">
                <AlertCircle size={16} className="text-white" />
                <h3 className="font-semibold text-white">Action Required</h3>
              </div>
              <p className="text-white text-sm">
                You have {blockingTasks.length} required task{blockingTasks.length > 1 ? 's' : ''} that must be completed.
              </p>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <div className="font-semibold text-[#333333]">{tasks.length}</div>
              <div className="text-[#797879]">Total Tasks</div>
            </div>
            <div className="text-center p-2 bg-green-50 rounded-lg">
              <div className="font-semibold text-green-700">
                {tasks.filter(t => t.status === 'completed').length}
              </div>
              <div className="text-green-600">Completed</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks List */}
      <div className="p-4">
        {filteredTasks.length > 0 ? (
          <div className="space-y-4">
            {filteredTasks.map((task) => (
              <div
                key={task.id}
                className={`bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden ${
                  task.status === 'completed' ? "opacity-75" : ""
                } ${task.status === 'pending' ? "hover:shadow-lg cursor-pointer transition-shadow duration-200" : ""}`}
                onClick={() => task.status === 'pending' && handleTaskAction(task.id)}
              >
                <div className="p-4">
                  <div className="flex">
                    <div
                      className={`w-10 h-10 border border-[#009639] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm ${
                        task.status === 'completed' ? "bg-white" : 
                        task.priority === 'blocking' ? "bg-white" :
                        task.priority === 'urgent' ? "bg-white" :
                        task.priority === 'normal' ? "bg-white" : "bg-white"
                      }`}
                    >
                      {task.status === 'completed' ? (
                        <CheckCircle size={20} className="text-[#009639]" />
                      ) : (
                        getTaskIcon(task.type, task.priority)
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3
                          className={`font-semibold ${
                            task.status === 'completed' ? "text-gray-600 line-through" : "text-[#333333]"
                          }`}
                        >
                          {task.title}
                        </h3>
                      </div>
                      
                      <p className={`text-sm mb-3 ${task.status === 'completed' ? 'text-gray-500' : 'text-[#797879]'}`}>
                        {task.status === 'completed' && task.completionText ? task.completionText : task.description}
                      </p>
                      
                      {/* Task metadata */}
                      <div className="flex items-center gap-4 text-xs text-[#797879] mb-3">
                        {task.estimatedMinutes && (
                          <div className="flex items-center gap-1">
                            <Timer size={12} />
                            {formatTimeEstimate(task.estimatedMinutes)}
                          </div>
                        )}
                        {task.dueDate && task.status === 'pending' && (
                          <div className="flex items-center gap-1">
                            <Calendar size={12} />
                            <span className={formatDueDate(task.dueDate)?.includes('Overdue') ? 'text-red-500' : ''}>
                              {formatDueDate(task.dueDate)}
                            </span>
                          </div>
                        )}
                        {task.status === 'completed' && task.completedAt && (
                          <div className="flex items-center gap-1">
                            <CheckCircle size={12} />
                            Completed {new Date(task.completedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>

                      {/* Action buttons below content */}
                      {task.status === 'pending' && (
                        <div className="flex items-center gap-2">
                          <Button
                            className="ride-primary-btn w-full py-2 text-sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTaskAction(task.id);
                            }}
                          >
                            {task.actionText}
                            <ChevronRight size={16} className="ml-1" />
                          </Button>
                          {task.priority === 'optional' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleTaskDismiss(task.id);
                              }}
                            >
                              <X size={16} />
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <CheckSquare size={40} className="text-[#d6d9dd] mx-auto mb-3" />
            <p className="text-[#333333] font-medium">No tasks match your filters</p>
            <p className="text-[#797879] text-sm mt-1">
              Try adjusting your filters or showing all tasks.
            </p>
          </div>
        )}
      </div>
    </PageWithScroll>
  );
}
