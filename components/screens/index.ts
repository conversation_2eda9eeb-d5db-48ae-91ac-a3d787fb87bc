// Screen registry for dynamic imports
export const SCREEN_COMPONENTS = {
  // Main navigation screens
  'home': () => import('./HomeScreen'),
  'vehicle-dashboard': () => import('./VehicleDashboardScreen'),
  'profile': () => import('./ProfileScreen'),
  'tasks': () => import('./TasksScreen'),
  'task-detail': () => import('./TaskDetailScreen'),
  'community': () => import('./CommunityScreen'),
  'opportunities': () => import('./OpportunitiesScreen'),
  
  // Vehicle-related screens
  'vehicle-details': () => import('./VehicleDetailsScreen'),
  'vehicle-status': () => import('./VehicleStatusScreen'),
  'vehicle-handover': () => import('./VehicleHandoverScreen'),
  'vehicle-search': () => import('./VehicleSearchScreen'),
  'list-vehicle': () => import('./ListVehicleScreen'),
  'vehicle-marketplace': () => import('./VehicleMarketplaceScreen'),
  
  // Listing management screens
  'listing-management': () => import('./ListingManagementScreen'),
  
  // Booking screens
  'booking-calendar': () => import('./BookingCalendarScreen'),
  'booking-calendar-new': () => import('./BookingCalendarNewScreen'),
  'booking-confirmation': () => import('./BookingConfirmationScreen'),
  'booking-details': () => import('./BookingDetailsScreen'),
  
  // Group management screens
  'my-groups': () => import('./MyGroupsScreen'),
  'group-details': () => import('./GroupDetailsScreen'),
  'group-finances': () => import('./GroupFinancesScreen'),
  'group-settings': () => import('./GroupSettingsScreen'),
  'group-chat': () => import('./GroupChatScreen'),
  'create-group': () => import('./CreateGroupScreen'),
  'member-details': () => import('./MemberDetailsScreen'),
  'member-management': () => import('./MemberManagementScreen'),
  'add-members': () => import('./AddMembersScreen'),
  // 'maintenance-schedule': () => import('./MaintenanceScheduleScreen'),
  // 'maintenance-details': () => import('./MaintenanceDetailsScreen'),
  // 'schedule-maintenance': () => import('./ScheduleMaintenanceScreen'),
  // 'edit-maintenance': () => import('./EditMaintenanceScreen'),
  // 'payment': () => import('./PaymentScreen'),
  // 'fraction-purchase': () => import('./FractionPurchaseScreen'),
  // 'purchase-confirmation': () => import('./PurchaseConfirmationScreen'),
  // 'income-tracking': () => import('./IncomeTrackingScreen'),
  // 'application-submitted': () => import('./ApplicationSubmittedScreen'),
  // 'entity-formation-confirmation': () => import('./EntityFormationConfirmationScreen'),
  // 'handover-confirmation': () => import('./HandoverConfirmationScreen'),
  // 'business-opportunity': () => import('./BusinessOpportunityScreen'),
  // 'tracking': () => import('./TrackingScreen'),
  // 'calendar-view': () => import('./CalendarViewScreen'),
  // 'upload-document': () => import('./UploadDocumentScreen'),
  // 'compliance-dashboard': () => import('./ComplianceDashboardScreen'),
  // 'help': () => import('./HelpScreen'),
  // 'feedback': () => import('./FeedbackScreen'),
  // 'notifications': () => import('./NotificationsScreen'),
  // 'privacy': () => import('./PrivacyScreen'),
  // 'legal-entity-formation': () => import('./LegalEntityFormationScreen'),
  // 'contract-management': () => import('./ContractManagementScreen'),
  // 'dispute-resolution': () => import('./DisputeResolutionScreen'),
  // 'profile-setup': () => import('./ProfileSetupScreen'),
  // 'ride-selection': () => import('./RideSelectionScreen'),
  // 'test-party-creation': () => import('./TestPartyCreationScreen'),
  // 'my-listing': () => import('./MyListingScreen'),
  // 'edit-listing': () => import('./EditListingScreen'),
  // 'not-authorized': () => import('./NotAuthorizedScreen'),
  // 'add-vehicle-to-group': () => import('./AddVehicleToGroupScreen'),

} as const;

export type ScreenName = keyof typeof SCREEN_COMPONENTS; 