"use client";

import { useState, useEffect } from "react";
import { ArrowLeft, UserPlus, Trash2, Edit, Mail, Phone, Crown, Users } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface MemberManagementScreenProps {
  params?: { groupId?: string };
}

interface Member {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  fraction: number;
  role: 'owner' | 'co-owner' | 'member';
  joinDate: Date;
  avatar?: string;
}

export default function MemberManagementScreen({ params }: MemberManagementScreenProps) {
  const { goBack, navigateToMemberDetails } = useNavigation();
  const groupId = params?.groupId || "1";
  
  const [loading, setLoading] = useState(true);
  const [showOwnershipModal, setShowOwnershipModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<string | null>(null);
  const [newOwnership, setNewOwnership] = useState<number>(0);

  // Mock members data
  const [members, setMembers] = useState<Member[]>([
    {
      id: "1",
      firstName: "John",
      lastName: "Smith", 
      email: "<EMAIL>",
      phone: "+27 82 555 0001",
      fraction: 40,
      role: "owner",
      joinDate: new Date('2023-10-15'),
      avatar: "/placeholder.svg?height=48&width=48"
    },
    {
      id: "2",
      firstName: "Sarah",
      lastName: "Johnson",
      email: "<EMAIL>", 
      phone: "+27 82 555 0002",
      fraction: 25,
      role: "co-owner",
      joinDate: new Date('2023-11-01'),
      avatar: "/placeholder.svg?height=48&width=48"
    },
    {
      id: "3",
      firstName: "Mike",
      lastName: "Wilson",
      email: "<EMAIL>",
      fraction: 20,
      role: "member",
      joinDate: new Date('2023-11-15'),
      avatar: "/placeholder.svg?height=48&width=48"
    },
    {
      id: "4",
      firstName: "Lisa",
      lastName: "Brown",
      email: "<EMAIL>",
      phone: "+27 82 555 0004",
      fraction: 15,
      role: "member",
      joinDate: new Date('2023-12-01'),
      avatar: "/placeholder.svg?height=48&width=48"
    }
  ]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleEditOwnership = (memberId: string, currentOwnership: number) => {
    setSelectedMember(memberId);
    setNewOwnership(currentOwnership);
    setShowOwnershipModal(true);
  };

  const handleSaveOwnership = () => {
    if (selectedMember) {
      setMembers(prev => prev.map(member => 
        member.id === selectedMember 
          ? { ...member, fraction: newOwnership }
          : member
      ));
    }
    setShowOwnershipModal(false);
    setSelectedMember(null);
  };

  const handleRemoveMember = (memberId: string) => {
    // Prevent removing the owner
    const member = members.find(m => m.id === memberId);
    if (member?.role === 'owner') {
      alert("Cannot remove the group owner");
      return;
    }
    
    if (confirm("Are you sure you want to remove this member?")) {
      setMembers(prev => prev.filter(member => member.id !== memberId));
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown size={14} className="text-yellow-500" />;
      case 'co-owner': return <Crown size={14} className="text-orange-500" />;
      default: return <Users size={14} className="text-gray-500" />;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-yellow-100 text-yellow-800';
      case 'co-owner': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalOwnership = members.reduce((sum, member) => sum + member.fraction, 0);

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
      <button className="mr-4" onClick={goBack}>
        <ArrowLeft size={24} className="text-white" />
      </button>
      <h1 className="text-xl font-bold text-white">Member Management</h1>
    </div>
  );

  if (loading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading members...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      {/* Summary */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-[#333333]">Group Overview</h2>
            <Badge className="bg-blue-100 text-blue-800">
              {members.length} Members
            </Badge>
          </div>
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{totalOwnership}%</p>
              <p className="text-xs text-[#797879]">Total Allocated</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{members.filter(m => m.role === 'owner').length}</p>
              <p className="text-xs text-[#797879]">Owners</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{members.filter(m => m.role === 'member').length}</p>
              <p className="text-xs text-[#797879]">Members</p>
            </div>
          </div>
        </div>
      </div>

      {/* Members List */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
          <div className="p-4 border-b border-gray-100">
            <h3 className="font-semibold text-[#333333]">Members ({members.length})</h3>
          </div>
          
          {members.map((member, index) => (
            <div
              key={member.id}
              className={`p-4 ${
                index < members.length - 1 ? "border-b border-[#f2f2f2]" : ""
              }`}
            >
              <div className="flex items-center mb-3">
                <div 
                  className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm cursor-pointer"
                  onClick={() => navigateToMemberDetails(member.id)}
                >
                  <span className="text-[#009639] font-medium text-lg">
                    {member.firstName.charAt(0)}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 
                      className="text-[#333333] font-semibold cursor-pointer hover:text-[#009639]"
                      onClick={() => navigateToMemberDetails(member.id)}
                    >
                      {member.firstName} {member.lastName}
                    </h3>
                    {getRoleIcon(member.role)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getRoleBadge(member.role)}>
                      {member.role.replace('-', ' ')}
                    </Badge>
                    <span className="text-xs text-[#797879] bg-[#f2f2f2] px-2 py-0.5 rounded-full">
                      {member.fraction}% ownership
                    </span>
                  </div>
                </div>
                <button
                  className="p-2 text-[#009639] hover:bg-[#e6ffe6] rounded-lg"
                  onClick={() => handleEditOwnership(member.id, member.fraction)}
                >
                  <Edit size={18} />
                </button>
              </div>
              
              {/* Contact Info */}
              <div className="flex items-center gap-4 text-sm text-[#797879] mb-3">
                <div className="flex items-center gap-1">
                  <Mail size={14} />
                  <span>{member.email}</span>
                </div>
                {member.phone && (
                  <div className="flex items-center gap-1">
                    <Phone size={14} />
                    <span>{member.phone}</span>
                  </div>
                )}
              </div>
              
              <div className="flex justify-between items-center mt-3 pt-3 border-t border-[#f2f2f2]">
                <span className="text-xs text-[#797879]">
                  Joined {member.joinDate.toLocaleDateString()}
                </span>
                <button
                  className={`text-sm px-3 py-1 rounded-full flex items-center gap-1 ${
                    member.role === 'owner' 
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                      : 'bg-red-100 text-red-600 hover:bg-red-200'
                  }`}
                  onClick={() => handleRemoveMember(member.id)}
                  disabled={member.role === 'owner'}
                >
                  <Trash2 size={14} />
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>

        <Button
          className="w-full bg-white rounded-xl shadow-md p-4 flex items-center justify-center text-[#009639] font-medium border border-gray-100 hover:bg-gray-50"
          variant="outline"
          onClick={() => {/* Navigate to add members */}}
        >
          <UserPlus size={20} className="mr-2" />
          Add New Member
        </Button>
      </div>

      {/* Ownership Distribution */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-semibold mb-3">
            Ownership Distribution
          </h3>

          <div className="h-4 bg-[#f2f2f2] rounded-full overflow-hidden flex mb-3">
            {members.map((member, index) => (
              <div
                key={member.id}
                className="h-full"
                style={{
                  width: `${member.fraction}%`,
                  backgroundColor: `hsl(${120 + index * 30}, 70%, 50%)`,
                }}
              ></div>
            ))}
          </div>

          <div className="flex flex-wrap gap-2">
            {members.map((member, index) => (
              <div
                key={member.id}
                className="flex items-center text-xs"
              >
                <div
                  className="w-3 h-3 rounded-full mr-1"
                  style={{
                    backgroundColor: `hsl(${120 + index * 30}, 70%, 50%)`,
                  }}
                ></div>
                <span>
                  {member.firstName} ({member.fraction}%)
                </span>
              </div>
            ))}
          </div>

          <Button
            variant="outline"
            className="w-full mt-4 border-[#009639] text-[#009639] hover:bg-[#e6ffe6]"
          >
            Reallocate Ownership
          </Button>
        </div>
      </div>

      {/* Ownership Modal */}
      {showOwnershipModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-5/6 max-w-md shadow-lg border border-gray-100">
            <h3 className="text-[#333333] font-semibold mb-4">
              Edit Ownership Percentage
            </h3>

            <div className="mb-4">
              <label className="block text-[#797879] text-sm mb-2">
                Ownership Percentage
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={newOwnership}
                onChange={(e) => setNewOwnership(Number.parseInt(e.target.value) || 0)}
                className="w-full px-4 py-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setShowOwnershipModal(false)}
              >
                Cancel
              </Button>
              <Button
                className="flex-1 bg-[#009639] hover:bg-[#007A2F] text-white"
                onClick={handleSaveOwnership}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}
    </PageWithScroll>
  );
} 