"use client";

import { useState, useEffect } from "react";
import { ArrowLeft, Plus, Clock, Check, X, AlertCircle, Mail, UserPlus, Search } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface AddMembersScreenProps {
  params?: { groupId?: string };
}

interface GroupInvitation {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fraction: number;
  status: 'sent' | 'accepted' | 'declined' | 'pending';
  sentAt: Date;
}

export default function AddMembersScreen({ params }: AddMembersScreenProps) {
  const { goBack, navigateToGroupDetails } = useNavigation();
  const groupId = params?.groupId || "1";
  
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  
  // Form state
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [fraction, setFraction] = useState(10);
  const [sending, setSending] = useState(false);

  // Mock invitations data
  const mockInvitations: GroupInvitation[] = [
    {
      id: "1",
      email: "<EMAIL>",
      firstName: "Alex",
      lastName: "Chen",
      fraction: 15,
      status: "sent",
      sentAt: new Date('2024-01-15T10:00:00')
    },
    {
      id: "2", 
      email: "<EMAIL>",
      firstName: "Maria",
      lastName: "Garcia",
      fraction: 20,
      status: "accepted",
      sentAt: new Date('2024-01-10T14:30:00')
    },
    {
      id: "3",
      email: "<EMAIL>", 
      firstName: "James",
      lastName: "Wilson",
      fraction: 10,
      status: "declined",
      sentAt: new Date('2024-01-05T09:15:00')
    }
  ];

  useEffect(() => {
    // Simulate loading invitations
    const timer = setTimeout(() => {
      setInvitations(mockInvitations);
      setLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const filteredInvitations = invitations.filter(
    (invitation) =>
      invitation.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSendInvitation = async () => {
    if (!firstName.trim() || !lastName.trim() || !email.trim()) {
      alert("Please fill in all required fields");
      return;
    }

    if (invitations.some((inv) => inv.email === email)) {
      alert("This email has already been invited");
      return;
    }

    setSending(true);
    try {
      // Simulate sending invitation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const newInvitation: GroupInvitation = {
        id: Date.now().toString(),
        email: email.trim(),
        firstName: firstName.trim(), 
        lastName: lastName.trim(),
        fraction,
        status: "sent",
        sentAt: new Date()
      };

      setInvitations(prev => [newInvitation, ...prev]);
      
      // Reset form
      setFirstName("");
      setLastName("");
      setEmail("");
      setFraction(10);
      setShowInviteForm(false);
      
      alert("Invitation sent successfully!");
    } catch (error) {
      console.error("Error sending invitation:", error);
      alert("Failed to send invitation");
    } finally {
      setSending(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    if (!confirm("Are you sure you want to cancel this invitation?")) {
      return;
    }

    try {
      // Simulate cancelling invitation
      await new Promise(resolve => setTimeout(resolve, 500));
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      alert("Invitation cancelled successfully");
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      alert("Failed to cancel invitation");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "sent":
        return <Clock size={16} className="text-yellow-600" />;
      case "accepted":
        return <Check size={16} className="text-green-600" />;
      case "declined":
        return <X size={16} className="text-red-500" />;
      default:
        return <AlertCircle size={16} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "sent":
        return "bg-yellow-100 text-yellow-800";
      case "accepted":
        return "bg-green-100 text-green-800";
      case "declined":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Add Members</h1>
      </div>
      <button
        className="w-10 h-10 bg-[#007A2F] hover:bg-[#006428] rounded-full flex items-center justify-center"
        onClick={() => setShowInviteForm(!showInviteForm)}
      >
        <Plus size={24} className="text-white" />
      </button>
    </div>
  );

  if (loading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading invitations...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      {/* Invite Form */}
      {showInviteForm && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center">
                <UserPlus size={16} className="text-[#009639]" />
              </div>
              <h2 className="text-lg font-semibold text-[#333333]">Send New Invitation</h2>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[#333333] mb-2">First Name *</label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="First name"
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-[#333333] mb-2">Last Name *</label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Last name"
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Email *</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Ownership Percentage</label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={fraction}
                  onChange={(e) => setFraction(Number(e.target.value))}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
              
              <div className="flex gap-3 pt-2">
                <Button
                  variant="outline"
                  onClick={() => setShowInviteForm(false)}
                  className="flex-1"
                  disabled={sending}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSendInvitation}
                  disabled={sending || !firstName.trim() || !lastName.trim() || !email.trim()}
                  className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
                >
                  {sending ? "Sending..." : "Send Invitation"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#797879]" />
          <input
            type="text"
            placeholder="Search invitations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent bg-white"
          />
        </div>
      </div>

      {/* Invitations List */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
          <div className="p-4 border-b border-gray-100">
            <h3 className="font-semibold text-[#333333]">
              Pending Invitations ({filteredInvitations.length})
            </h3>
          </div>
          
          {filteredInvitations.length > 0 ? (
            filteredInvitations.map((invitation, index) => (
              <div
                key={invitation.id}
                className={`p-4 ${
                  index < filteredInvitations.length - 1 ? "border-b border-[#f2f2f2]" : ""
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                      <span className="text-[#009639] font-medium text-lg">
                        {invitation.firstName.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-[#333333] font-medium">
                        {invitation.firstName} {invitation.lastName}
                      </h4>
                      <p className="text-sm text-[#797879]">{invitation.email}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getStatusColor(invitation.status)}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(invitation.status)}
                            {invitation.status}
                          </span>
                        </Badge>
                        <span className="text-xs text-[#797879] bg-[#f2f2f2] px-2 py-0.5 rounded-full">
                          {invitation.fraction}% ownership
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {invitation.status === 'sent' && (
                    <button
                      onClick={() => handleCancelInvitation(invitation.id)}
                      className="text-red-600 hover:bg-red-100 px-3 py-1 rounded-lg text-sm"
                    >
                      Cancel
                    </button>
                  )}
                </div>
                
                <div className="mt-3 text-xs text-[#797879]">
                  Sent {invitation.sentAt.toLocaleDateString()} at {invitation.sentAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
            ))
          ) : (
            <div className="p-6 text-center">
              {searchQuery ? (
                <>
                  <Search size={48} className="text-[#797879] mx-auto mb-4" />
                  <p className="text-[#797879]">No invitations found matching "{searchQuery}"</p>
                </>
              ) : (
                <>
                  <Mail size={48} className="text-[#797879] mx-auto mb-4" />
                  <p className="text-[#797879]">No pending invitations</p>
                  <p className="text-sm text-[#797879] mt-2">
                    Tap the + button to invite new members
                  </p>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
          <h3 className="text-[#333333] font-semibold mb-3">Quick Actions</h3>
          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigateToGroupDetails(groupId)}
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Group Dashboard
            </Button>
          </div>
        </div>
      </div>
    </PageWithScroll>
  );
} 