"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface ScrollContainerProps {
  children: React.ReactNode;
  className?: string;
  paddingBottom?: "pb-16" | "pb-20" | "pb-24" | "pb-32";
}

export function ScrollContainer({ 
  children, 
  className = "",
  paddingBottom = "pb-24"
}: ScrollContainerProps) {
  return (
    <div className={cn("flex-1 overflow-y-auto scrollbar-hide", className)}>
      <div className={paddingBottom}>
        {children}
      </div>
    </div>
  );
}

interface PageWithScrollProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  tabs?: React.ReactNode;
  className?: string;
  paddingBottom?: "pb-16" | "pb-20" | "pb-24" | "pb-32";
}

export function PageWithScroll({ 
  children, 
  header, 
  tabs, 
  className = "",
  paddingBottom = "pb-24"
}: PageWithScrollProps) {
  return (
    <div className={cn("h-screen flex flex-col", className)}>
      {/* Fixed Header */}
      {header && (
        <div className="flex-shrink-0">
          {header}
        </div>
      )}
      
      {/* Fixed Tabs */}
      {tabs && (
        <div className="flex-shrink-0">
          {tabs}
        </div>
      )}
      
      {/* Scrollable Content */}
      <ScrollContainer paddingBottom={paddingBottom}>
        {children}
      </ScrollContainer>
    </div>
  );
} 